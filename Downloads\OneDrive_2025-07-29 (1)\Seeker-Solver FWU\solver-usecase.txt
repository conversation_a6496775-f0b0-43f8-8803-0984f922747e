## Registration :

### **Use Case 1: Register with Email**

- A new solver navigates to the registration page, selects "Register as Solver," enters their email, creates and confirms a password.
- The system checks the password against a known data breach database. After the solver accepts the Code of Conduct and EULA, they click "Register."
- A hidden reCAPTCHA validates the user is not a bot.
- The system creates the account, assigns a default solver badge, and sends a welcome email containing a getting started guide and a one-time backup code (regex-generated, SHA-512 hashed).

### **Use Case 2: Register via SSO (Gmail / Microsoft / Apple / GitHub)**

- The solver selects a single signin on login option.
- After successful authentication with the chosen provider, the system retrieves the necessary user data, checks for any existing account, and creates a new solver account if none exists.
- A default badge is assigned, and a welcome email with a getting started guide and backup code is sent((regex-generated, SHA-512 hashed).

### **Use Case 3: Password Checked Against Data Breach**

- During email registration, the solver enters a password.
- The system validates the password for complexity and checks it against a data breach service.
- If the password is found in a breach, the system blocks submission and asks the user to create a more strong & secure password.

### **Use Case 4: Accept Code of Conduct and EULA**

- Before account creation, the solver must accept the platform’s Code of Conduct and EULA.
- If either checkbox is left unchecked, the system prevents registration and prompts the user to agree before proceeding.

### **Use Case 5: Hidden reCAPTCHA Validation**

- Upon clicking "Register," a hidden reCAPTCHA runs in the background to detect automated bots.
- If the test is passed, the registration continues. If failed, the system blocks the request and shows an error indicating suspicious activity.

### **Use Case 6: Badge Assignment**

- Once the solver's account is successfully created, the system assigns a default badge (e.g., “Solver – Newbie”) to indicate their role on the platform.
- This badge is reflected on their profile and in community spaces.

### **Use Case 7: Send Welcome Email with Backup Code**

- After account creation, the system sends a welcome email to the solver’s registered mail id.
- This includes a getting started guide and a one-time backup code generated using a secure regex pattern (e.g., `[a-z][0-9][a-m][0-9][n-z][A-Z]`), which is hashed with SHA-512 before storage.

---

# Login :

### **Use Case 1: Login via Email or Mobile (OTP or Magic Link)**

- A registered user enters their email or mobile number on the login screen.
- The system offers a choice between receiving a one-time password (OTP) or a magic link.
- Once the user verifies using either method, the system authenticates the session
- A hidden reCAPTCHA validates the user is not a bot.
- The system grants permission to the user.

### **Use Case 2: SSO Login (Gmail / Microsoft / Apple / GitHub)**

**Scenario:**

The user selects a social login option. After successful authentication with the chosen provider, the system validates the credentials and logs the user in. If it's their first login, the system completes registration flow; otherwise, it directs them to their dashboard.

### **Use Case 3: Login via Password**

- The user enters their email or mobile and password.
- They optionally click the “Show Password” toggle to reveal the input.
- Upon submission, the system authenticates the credentials with the database. If correct, login is successful; otherwise, an error is shown.

### **Use Case 4: Remember Me**

- The user selects the “Remember Me” checkbox.
- The system issues a long-lived token with an extended expiration (e.g., 30 days). When the user returns, they remain logged in unless manually logged out.

### **Use Case 5: Trouble Signing In**

- User clicks on the “Trouble Signing In?” button. The system redirects them to recovery options, including Forgot Password and Forgot Account via backup code.

### **Use Case 6: Forgot Password (OTP or Magic Link)**

The user selects “Forgot Password,” chooses either OTP or magic link for verification. Upon successful verification, they can reset their password and log in.

### **Use Case 7: Forgot Account via Backup Code**

- If the user lost access to email/phone. They select “Forgot Account” and enter their previously issued backup code.
- If the code matches the stored hash, access is recovered or alternate recovery options are provided.

### **Use Case 8: Rate Limiting Login Attempts**

- If the user  attempts multiple failed logins within a short time.
- The system detects this and temporarily blocks further attempts from that source, showing a warning or cooldown message.

### **Use Case 9: Hidden reCAPTCHA on Login**

- When the user submits login credentials or OTP, a hidden reCAPTCHA runs in the background. If validation fails, the login is blocked, suspecting automated or malicious activity.

### **Use Case 10: Redirect to Sign-Up**

- An unregistered user tries to log in and fails. They’re prompted with a "No account? Sign up" link, which redirects them to the registration page.

---

# Dashboard

### **Use Case 1: Switch Theme (Dark / Light Mode)**

- The solver toggles between dark and light mode from the dashboard interface. The selected preference is saved in their profile and applied across sessions.

### **Use Case 2: Create Tutorial & View Earnings**

- The solver clicks on “Create Tutorial” to publish content. They’re taken to a content creation form where they add title, media, tags, and description. Upon submission.
- The solver clicks on “View earnings” to view his/her payment page.

### **Use Case 3: Analytics Dashboard**

The solver accesses a panel showing visual insights:

- Content growth  - Show’s the content growth over a month
- Solved Problem - Number of doubts solved
- Daily/weekly streaks
- Badges earned
- Sessions taught - Total completed sessions
- Growth graph (followers/ratings)
- Reached Content  (views)
- Total Earnings - Total hours taught in the sessions

### **Use Case 4: View Active Sessions**

- Solver sees a list of sessions currently in progress or awaiting their participation.
- Clicking a session shows full details with options like “Join Now,” “Reschedule,” or “Cancel.”

### **Use Case 5: Recent Earnings**

- The solver  recent session payments will be displayed here.

### **Use Case 6: Available Slots (Calendar View)**

- Solver opens their availability calendar and adds, edits, or removes time slots. .

### **Use Case 7: Review Sessions Before Accepting**

- Solver receives new session requests. Before confirming, they can review session details (seeker info, topic, proposed time). They can accept or decline.

### **Use Case 8: Upcoming Sessions (Preview)**

- It displays the next 2–3 upcoming confirmed sessions with date, time, and seeker info. Clicking a session shows view details or join session

### **Use Case 9: Expert Profile**

- Solver expertise and reputation i.e current rate/hr and expertise domains

### **Use Case 9: Schedule**

- Solvers Today’s and Tomorrow schedule

### **Use Case 10: View and Respond to Anonymous Doubt Pools**

- **Active Requests:** Real-time doubt entries from seekers.
- Clicks to **View Details & Respond**.
- On submission, the answer is posted anonymously with bounty claimed.

### **Use Case 11: View Your Content**

- Solver accesses their published content list.

---

# Bookings & Sessions

### **Use Case 1: Profile Booking Link**

- Its a  solver's public booking link (from profile)

### **Use Case 2: View Upcoming & Active Sessions**

- the “Bookings” tab to see upcoming confirmed sessions and any sessions currently in progress. Clicking an item provides session details and access options (join, reschedule, cancel).

### **Use Case 3: Podcast Session with Seeker**

- Solver hosts a podcast-format session booked by the seeker. It follows a more structured, interview-style format. The system supports recording, scheduling, and publishing after session ends.

### **Use Case 4: Availability Analytics + Calendar Integration (Google/Microsoft)**

- Solver views analytics on when sessions are most booked. Availability is auto-updated from external calendars.

### **Use Case 5: Availability Analytics**

- Weekly Availability - It shows the solver weekly availability increase in  percentage
- Booking rate - It shows the Booking rate
- Peak hours - Solver most active time
- No-show rate
- View deatiled analytics - It shows more more deatiled analytics

### **Use Case 6: Completed Sessions**

- Solver accesses the list of completed sessions. For sessions that were recorded, a playback option is available.

### **Use Case 7: Rescheduled or Cancelled Sessions**

- Solver requests to reschedule.  The new time is set and reflected in dashboards.

### **Use Case 8: Bulk Minute Sessions / Team Sessions**

- If  seeker books a pack of minutes (e.g., 60 min in bulk) or books for a group (team session). The solver receives a consolidated request or hosts a multi-user mentoring session.

### **Use Case 9: In-Call Features (Screen Share, Voice, Reactions)**

- During the session | Solver can share their screen | Voice controls allow mute/unmute and “mute all.” | Seekers can raise hands or use emoji reactions .
- **In-Call Chat -** Solver and seeker use a live chat panel during the call
- **Reminders Before Session (with Beep Alert) - a beep alert will be coming to the solver to remin about the session**
- **Extend Session -** Near session end, the  seeker can request an extension.
- **Whiteboard Access -** Solver activates a digital whiteboard tool in-session. Both users can draw, type, or upload images for visual explanation.
- **Live Captions -** The session auto-generates real-time captions.
- **Share Files During Session -** Solver uploads a resource (PDF, DOCX, PPT) during the session.
- **Post-Session Review -** After the session, both solver and seeker are prompted to rate the experience and optionally leave a review.
- **Face Verification Before Session -** Before the session begins, the platform prompts the solver for a quick face verification check to prevent impersonation or misuse. If successful, they are allowed to join.

---

# Profile

### **Use Case 1: Profile Completion Meter**

- As the solver fills in profile fields (bio, profile picture, skills, availability, etc.), the system displays a dynamic percentage meter showing how complete their profile is.
- Completing 100% may unlock certain features or rewards.

### **Use Case 2: View Followers & Expertise Tags**

- On the solver’s public, users can view the number of followers and areas of expertise .

### **Use Case 3: Edit Personal Information**

- Solver updates basic information like first/last name, country, city, gender, DOB, profile picture, and bio.

### Use Case 4: Pseudo name

- Solver can set up a Psedo name to enter into the community
- This can only setup once.

### **Use Case 5: Change Email or Mobile Number (OTP / Magic Link)**

- Solver initiates a change to their registered email or mobile number. The system sends an OTP or magic link to the **new** contact method.

### **Use Case 6: Change Password**

- Solver updates their password through account settings. The system requests current password, validates the new one (strength & breach check), then applies changes securely.

### **Use Case 7: Add Bank Account Details**

- Solver navigates to the payment section and he can view his earnings .

### **Use Case 8: Set Weekly Availability Schedule**

- Solver updates their weekly time slots to reflect when they are open for sessions and podcast bookings.

### **Use Case 9: Add Skills with Validation**

- Solver adds skills.  Skills are validated before being saved or displayed.

### **Use Case 10: Add Projects**

- Solver adds up about the Projects , Experience

Each section supports rich text, file upload, and verification .

### **Use Case 11 : Deactivate or Delete Account**

- Solver selects "Deactivate" to pause account visibility (retains data), or "Delete" to permanently remove their data.

---

# Skill Verification

### **Use Case 1: Submit Portfolio & Projects**

- Solver  submitting relevant project links, case studies, & a personal portfolio. The system accepts various file types and URLs, associates them with the claimed skill, and sends them for manual or automated review. If approved, the skill is marked as "Verified".

### **Use Case 2: Record and Submit Live Demo (1–2 min Introduction Video)**

- Solver records a short live video from laptop introducing themselves and demonstrating their expertise and also they should keep their mobile in recording for the keyboard and hands .  This video is uploaded and placed “under observation”. Admin will be validating the video and approves or disapproves.

### **Use Case 3: Community Validation (Help ≥ 5 Seekers)**

- Solver must actively contribute to the platform by helping at least 5 different seekers via anonymous doubt pools or mentorship sessions. Impressions and reviews will be validated.

---

# KYC Verification

### **Use Case 1: Light Verification (Email & Phone)**

- A new solver completes light verification during registration or profile setup by verifying their email and phone number

### **Use Case 2: Standard Verification (Government ID Upload)**

- Solver wants to unlock more features ,They should upload a valid government-issued ID (e.g., passport, license)

### **Use Case 3: Full Verification (ID + Dynamic Facial Authentication)**

- Before engaging in high-value actions (e.g., handling large wallet balances, payouts, high-ticket sessions), the user is prompted for full verification.
- **Real-time facial recognition** (selfie video or blink/head-turn check)

---

# Payments

### **Use Case 1: Select Mode of Payment**

- During checkout, they choose solver preferred payment mode (e.g., UPI, Stripe, Wallet). The system processes the payment securely and updates the transaction log.

### **Use Case 2: View Wallet Balance & Top-up**

- **S**olver checks their in-platform wallet to see total available earnings (from completed sessions, doubt pools etc.). Balance is updated in real-time after each verified session.

### **Use Case 3: Session-wise Earnings Overview**

- A solver views earnings per session, categorized by session type (1:1, podcast, group). Each entry displays session ID, duration, date, and earning amount.

### **Use Case 5: Transaction History**

- Solvers can access a detailed transaction history that includes Session payments , earnings .

### **Use Case 6: Transfer to Bank Account**

- Solver requests to withdraw their wallet balance to their linked bank account.
- The transaction is logged and marked as “Pending” or “Completed.”

### **Use Case 7: Export Payment Data (Statements)**

- Solver clicks “Export Statement” in the payments section. The platform generates a downloadable CSV/PDF file with transaction summaries for a selected time range—useful for invoices, accounting, or taxes.

---

# Content

### **Use Case 1: Upload Content**

- Solver wants to share educational material. They go to the content upload section, choose a format (Reel, Blog, Video, Link, PDF, GitHub repo, image), and upload it. The system checks file type, size, and category compliance before publishing or saving as a draft.
- It can be tagged for free or premium.

### **Use Case 2: Tag Content with Category, Label, and Hashtags**

- While uploading content, the solver assigns Labeling, Hashtags for the usage of filters
- The system restricts labels to valid categories and flags any invalid tags.

### **Use Case 3: View Engagement Metrics**

- After publishing, the solver can track content performance views, likes etc
- These are shown in real-time analytics and can help solvers improve their reach.

### **Use Case 4: Podcast (Individual or Group)**

- Solver starts a podcast (either solo or group)
- Podcast metadata is stored as content, linked to analytics and discoverability features.
- Solver can share podcast video in public or private

### **Use Case 5 : Delete or Draft Content**

- Solver decides to remove or temporarily hide a post. They can: Delete it permanently or Move it to **Draft** to update and re-publish later

### **Use Case 6: Share Content via Link**

- Solver clicks “Share” on any of their posts. The platform generates a unique public or restricted link, which can be shared across social media .

---

# Community

### **Use Case 1: View and Join Active Communities**

- A solver navigates to the community hub and sees a list of active communities. The solver can only join anonymously, using their pseudo name.

### **Use Case 2: Participate in Group chats**

- Solver enters a discussion thread and start enaging in the group chat

### **Use Case 3: Share Content into Communities**

- Solver can upload a blog , posts , video any content , members can comment and react within the community thread.

### **Use Case 4: Events**

- Solver visits the Events tab under Community and browse the upcoming , ongoing events or workshops.

### **Use Case 5: Conferences**

- Solver can check and register for any conferences posted under this section.

---

# Settings

### **Use Case 1: Manage Notification Settings**

- Solvercustomizes notifications by enabling or disabling email alerts, calendar reminders with notes, browser push notifications, message alerts, weekly digests, and notifications for anonymous pool responses.

### **Use Case 2: Review Privacy Disclaimer**

- Solver opens the privacy disclaimer to understand how their personal data, activity, and identity are handled by the platform.

### **Use Case 3: Access Platform Policies**

- Solver reviews subscription, reporting, referral, and payment policies to stay informed about platform rules and procedures.

### **Use Case 4: Enable Multi-Factor Authentication (MFA)**

- Solver activates MFA, adding an extra verification step (e.g., OTP or authenticator app) when signing in for enhanced account security.

---

# Support

### **Use Case 1: Contact via Email**

- Solver encounters a technical issue <NAME_EMAIL>. The support team responds with troubleshooting steps or escalates the issue if needed.

### **Use Case 2: Get Real-Time Help via Chat**

- Solver needs quick assistance with booking and opens the Help Desk Chat, receiving instant support from an agent.

### **Use Case 3: Resolve Payment Queries**

- If Solver has a billing issue and raises a payment-related query through support. The support team checks transaction details and provides resolution.

### **Use Case 4: Browse FAQs**

- Solver  explores the FAQ section to find quick answers about chat usage, payment processes, refunds, and platform policies.

### **Use Case 5: Manage Support Chats**

- Solver reviews previous support conversations, clears unnecessary messages, or deletes entire chat histories for privacy.

---

# LOGOUT