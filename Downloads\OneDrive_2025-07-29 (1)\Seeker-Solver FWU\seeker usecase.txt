# SEEKER USECASES

1. Register

### **Use Case 1: Register with Email**

**Scenario:**

→A new user navigates to the registration page, enters their email, creates a password, and confirms it. 

→The system validates the password against known data breaches. 

→After the user accepts the EULA, they click "Register." 

→The hidden reCAPTCHA validates they’re not a bot, the account is created, and a welcome email with a backup code is sent.

### **Use Case 2: Register via SSO (Gmail/Microsoft/Apple/GitHub)**

**Scenario:**

→The user chooses a social login option 

→authenticates through the selected provider, and is redirected back. 

→The system verifies the credentials, creates the account, and sends the welcome email with the backup code.

### **Use Case 3: Register Using a Referral Code**

**Scenario:**

→During registration, a user enters a referral code. 

→The system verifies the code, applies referral rewards to both the referrer and new user, and proceeds with account creation.

### **Use Case 4: Password Breach Validation**

**Scenario:**

→<PERSON>hile setting up a password, the system checks if the chosen password exists in known data breaches. 

→If compromised, the user is prompted to select a new password before account creation can continue.

### **Use Case 5: Accepting Disclaimer and EULA**

**Scenario:**

→Before clicking the register button, the user reviews and accepts the disclaimer and EULA. Registration cannot proceed until these terms are accepted.

### **Use Case 6: Hidden reCAPTCHA Validation**

**Scenario:**

→When the user clicks "Register," the hidden reCAPTCHA runs in the background to detect automated sign-ups. 

→If passed, the account creation proceeds; if not, the registration is blocked.

### **Use Case 7: Welcome Email with Backup Code**

**Scenario:**

→After successful registration, the user receives an automated email with a getting-started guide and a secure backup code 

---

1. Login

### **Use Case 1: Login via Email or Mobile with OTP**

**Scenario:**

→A user enters their registered email or mobile number and requests a one-time password. 

→The system sends the OTP via email or SMS. 

→The user enters the OTP, and upon validation, gains access to their account.

### **Use Case 2: Login via Magic Link**

**Scenario:**

→A user chooses to receive a magic link. 

→The system emails the secure link, which the user clicks to be automatically logged into their account.

### **Use Case 3: Login with Password**

**Scenario:**

→A user enters their email and password. 

→They can toggle the “Show Password” option to confirm.

→If the credentials are valid, they’re logged in; otherwise, an error prompts re-entry.

### **Use Case 4: SSO Login**

**Scenario:**

→The user clicks "Login with Google," authenticates on Google’s portal, and is redirected back. 

→The system verifies the token and grants access.

### **Use Case 5: Remember Me**

**Scenario:**

→A user selects “Remember Me” before logging in. 

→The system issues a secure token with an expiration period, keeping them signed in across sessions until the token expires or they log out.

### **Use Case 6: Trouble Signing In**

**Scenario:**

→A user struggles to log in and clicks "Trouble Signing In," which provides guided help such as FAQs, password reset, or contacting support.

### **Use Case 7: Forgot Password / Account Recovery**

**Scenario:**

→A user selects “Forgot Password” and receives an OTP or magic link to reset their password. 

→If they can’t access their account, they use their backup code to regain access.

### **Use Case 9: Redirect to Sign-Up**

**Scenario:**

→A new visitor on the login page clicks “No Account? Sign Up” and is redirected to the registration page to create a new account.

---

1. Dashboard 

### **Use Case 1: Switch Theme**

**Scenario:**

→A user opens their dashboard and switches between dark and light modes for better visibility depending on their environment.

### **Use Case 2: View Notifications**

**Scenario:**

→A seeker logs in and checks notifications for upcoming bookings, streak updates, reward milestones, and responses from solvers. They can dismiss or act on each alert.

### **Use Case 3: Track Streak**

**Scenario:**

→A user views their current streak on the dashboard, showing the number of consecutive days of engagement. 

→The streak motivates them to continue using the platform for reward milestones.

### **Use Case 4: Analyze Activity**

**Scenario:**

→A solver opens analytics to view the total number of problems solved, bookings completed, money earned/spent, and minutes spent today, weekly, monthly, and yearly, along with their current level progression.

### **Use Case 5: Explore Trending Solvers and Content**

**Scenario:**

→A seeker browses trending solvers, topics, and content recommendations tailored to their interests, enabling them to discover new mentors and learning material.

### **Use Case 6: Check Upcoming Bookings**

**Scenario:**

→A seeker checks the list of their scheduled mentorship sessions, including session times, solvers’ details, and the option to reschedule if needed.

### **Use Case 7: Manage Calendar**

**Scenario:**

→A seeker syncs their platform calendar with Google Calendar to manage available slots, upcoming mentorship sessions, and reminders for upcoming meetings.

### **Use Case 8: Review Anonymous Pool Responses**

**Scenario:**

→A seeker checks replies to their questions in the anonymous pool directly from the dashboard and engages with solvers who provided answers.

---

1. Explore 

### **Use Case 1: View Search Results**

**Scenario:**

→A seeker enters a query in the search bar. 

→The system displays labeled results of relevant solvers, topics, and content that match their search.

### **Use Case 2: Discover Suggested Solvers**

**Scenario:**

→After selecting a preferred category, a seeker is shown a list of recommended solvers based on their interests, previous bookings, and solver ratings.

### **Use Case 3: Search and Book Solvers**

**Scenario:**

→A seeker searches for a mentor, views their profile with expertise details, and clicks “Book Now” to schedule a session.

### **Use Case 4: Apply Advanced Filters**

**Scenario:**

→A seeker uses filters such as category, sub-category, country, pricing, rating, tier, specialty, availability, and language to narrow down search results for an ideal mentor.

### **Use Case 5: Send Gift or Donation**

**Scenario:**

→A seeker chooses to gift a mentorship session to another user or donate to support solvers, completing the transaction using their preferred payment method.

### **Use Case 6: Share Platform**

**Scenario:**

→A user clicks “Share Platform” to send referral links through social media, messaging apps, or email, inviting new users to join.

### **Use Case 7: Explore Podcasts**

**Scenario:**

→A seeker browses the podcast section, discovers and books a slot with solver to have podcast session with solver and proceeds with the preferred payment method

---

1. **Chat/Bookings/Sessions**

### **Use Case 1: Face Verification Before Session**

**Scenario:**

→A seeker joins a scheduled session, and the system prompts for real-time face verification to confirm identity. 

→Access is granted only after successful verification.

### **Use Case 2: Book Anonymous or Group Sessions**

**Scenario:**

→A seeker books either an anonymous one-on-one session or a group mentorship session with multiple participants, selecting preferred times from solver availability.

### **Use Case 3: Access Shared Files**

**Scenario:**

→During a session, a seeker downloads reference materials, notes, or resources uploaded by the solver to the shared files section.

### **Use Case 4: Manage Booking History**

**Scenario:**

→A user reviews past mentorship sessions, checking dates, session details, and payment records for reference or scheduling again.

### **Use Case 5: Schedule, Reschedule, or Cancel Sessions**

**Scenario:**

→A seeker schedules a new session, reschedules, or cancels according to the no-refund cancellation policy.

### **Use Case 6: Engage in Query Pools**

**Scenario:**

→A seeker posts a query in the pool, views responses from solvers, review the response and react for it.

### **Use Case 7: Real-Time Collaboration Tools**

**Scenario:**

→During a session, seeker and solver communicate via chat or call, share screens, use a whiteboard for brainstorming, take notes, and record the meeting for future reference.

### **Use Case 8: Voice and Reactions Control**

**Scenario:**

→Participants mute/unmute themselves, mute all when necessary, raise hands to speak, react with emojis, and upload reference files under platform restrictions.

### **Use Case 9: Manage Chat History**

**Scenario:**

→A user reviews past conversations, clears unnecessary chats, or permanently deletes them as per the platform’s chat deletion policy.

### **Use Case 10: Session Reminders**

**Scenario:**

The system sends alerts before scheduled sessions and another reminder one minute prior before the session ends with a short beep sound.

### **Use Case 11: Track and Extend Session Time**

**Scenario:**

→A timer displays remaining session time, and a seeker may request an extension if more discussion is needed.

### **Use Case 12: Enable Live Captions**

**Scenario:**

→During sessions, captions are displayed in real time to aid understanding and accessibility.

### **Use Case 13: Provide Session Review**

**Scenario:**

→After a session, a seeker leaves feedback and ratings for the solver, helping improve quality and trust in the platform.

### **Use Case 14: Book and Pay for Podcasts**

**Scenario:**

→A seeker browses solver-hosted podcasts, books a slot, after the podcast session based on the time the payment is calculated 

---

1. Profile

### **Use Case 1: Track Profile Completion**

**Scenario:**

→A seeker views their profile percentage meter, showing how much of their profile (personal details, picture, bio, and verification) is complete, encouraging them to finish setup.

### **Use Case 2: Manage Personal Information**

**Scenario:**

→A user updates first name, last name, country, city, gender, date of birth, profile picture, and bio. They can also deactivate or permanently delete their account when needed.

### **Use Case 3: Change Email or Mobile Number**

**Scenario:**

→A user updates their registered email or phone number. 

→The system verifies the change via OTP or magic link and sends a notification to the old contact for security.

### **Use Case 4: Change Password**

**Scenario:**

→A user requests to update their password. They receive an OTP or magic link on their email or mobile to confirm identity before the new password takes effect.

### **Use Case 5: Set Anonymous Identity**

**Scenario:**

→A user chooses an anonymous identity (pseudonym) for community interactions. 

→Once set, it cannot be changed, ensuring platform consistency and privacy.

---

1. KYC Verification

### **Use Case 1: Complete Light Verification**

**Scenario:**

→A new user verifies their account by confirming email and phone number through OTPs. 

→This grants access to basic features and improves account trust.

### **Use Case 2: Complete Standard Verification**

**Scenario:**

→A seeker wants to unlock higher booking limits. 

→They upload a government-issued ID, which the system checks for authenticity. Upon approval, their verification status upgrades to "Standard."

### **Use Case 3: Complete Full Verification**

**Scenario:**

→A solver preparing for high-value transactions undergoes full verification. 

→They upload an ID and perform dynamic facial authentication (e.g., live selfie match). 

→After approval, they gain access secure platform services.

---

1. Content

### **Use Case 1: Browse Categorized Content**

**Scenario:**

→A seeker explores mentorship resources organized by categories such as topics, learning levels, and formats for easy discovery.

### **Use Case 2: Apply Content Filters**

**Scenario:**

→A user sets filters by topic, sub-topic, and content type (e.g., blogs, videos) to narrow down results and find relevant material quickly.

### **Use Case 3: Access Various Media Formats**

**Scenario:**

→A seeker watches 30–60 second reels, longer videos, live sessions, snippets, resources, and blogs to learn from solvers in different formats.

### **Use Case 4: View Solver Profile and Actions**

**Scenario:**

→A seeker taps on the solver’s profile from a content piece, views their expertise, ratings, books a session, or saves the content for later.

### **Use Case 5: Engage with Content**

**Scenario:**

→Users comment on reels using their usernames, award badges to creators, and react with likes or emojis to appreciate content.

### **Use Case 6: Follow Channels**

**Scenario:**

→A seeker follows solver channels to get notified of new content and stay updated on topics they’re interested in.

### **Use Case 7: Access Premium Content via Subscription**

**Scenario:**

→A seeker with a subscription bypasses content throttling limits and gains unlimited access to premium learning resources.

### **Use Case 8: Report Duplicate Content**

**Scenario:**

→A user flags impersonated or duplicate content from other platforms. The system reviews and, if validated, removes the infringing material.

### **Use Case 9: Organize Saved Content**

**Scenario:**

A seeker saves content, solvers, reels, blogs, and links into custom folder categories for better organization and quick access.

---

1. Rewards

### **Use Case 1: Earn Rewards for Verification**

**Scenario:**

→A seeker completes email, mobile, ID, face, and full profile verification, earning reward points that can be used as to book session and more.

### **Use Case 2: Receive Signup Bonus**

**Scenario:**

→A new user creates an account and immediately receives a signup bonus credited to their wallet, encouraging them to explore bookings.

### **Use Case 3: Gain Rewards for Referrals**

**Scenario:**

→A seeker refers a friend using a referral code. 

→Once the friend registers and completes verification, both accounts receive referral rewards.

### **Use Case 4: Unlock Subscription Rewards**

**Scenario:**

→A seeker subscribes to a premium plan and earns loyalty rewards such as extra content access or discounted booking rates.

### **Use Case 5: Maintain Streak Rewards**

**Scenario:**

→A user engages daily with the platform earns rewards. 

→After maintaining a monthly or quarterly activity streak, they receive streak-based reward bonuses.

### **Use Case 6: Report Solvers for Rewards**

**Scenario:**

→A seeker reports a solver for misconduct or unusual activity going on the platform. 

→Upon review and confirmation, the seeker earns a reward for helping maintain community standards.

### **Use Case 7: Earn Discounts on Bookings**

**Scenario:**

→A seeker books multiple mentorship sessions or accumulates a high number of minutes, unlocking tiered discounts on future bookings.

---

1. Payments

### **Use Case 1: Select Preferred Currency**

**Scenario:**

→A seeker chooses their preferred currency for all transactions, ensuring consistent pricing across bookings and subscriptions.

### **Use Case 2: Manage Payment Methods**

**Scenario:**

→A user adds, edits, or deletes credit cards, UPI, or other payment methods, with the option to pay directly using earned coins.

### **Use Case 3: Add Money to Wallet**

**Scenario:**

→A seeker tops up their wallet with at least the minimum amount (e.g., $50) to use for bookings, subscriptions, and in-app purchases.

### **Use Case 4: View Transaction Status and History**

**Scenario:**

→A user checks real-time payment statuses and reviews their complete transaction history for financial tracking.

### **Use Case 5: Manage Refunds**

**Scenario:**

→A seeker requests a refund for a canceled session, tracks the refund status, and reviews their refund history for records.

### **Use Case 6: Export Payment Data**

**Scenario:**

→A solver exports their transaction history and earnings data for accounting or tax purposes.

### **Use Case 7: Utilize Referral Bonus**

**Scenario:**

→A user applies referral bonuses earned from inviting friends as credits toward bookings or wallet balance.

### **Use Case 8: Get Discounts for Bulk Minutes**

**Scenario:**

→A seeker buys mentorship minutes in bulk and receives a discount, reducing overall session costs.

### **Use Case 9: Apply Coupons or Promo Codes**

**Scenario:**

→A user enters a promotional code during checkout to unlock special discounts on sessions or subscriptions.

### **Use Case 10: Make Subscription Payments**

**Scenario:**

→A seeker subscribes to a monthly or annual plan, with the system processing recurring payments automatically.

### **Use Case 11: Split Group Payments**

**Scenario:**

→Multiple seekers book a group mentorship session and use the group split feature to share the session fee evenly among participants.

---

1. Community 

### **Use Case 1: Maintain Solver Anonymity**

**Scenario:**

→A solver participates in community discussions using their pseudonymous identity, ensuring privacy while sharing mentorship insights.

### **Use Case 2: Discover and Join Communities**

**Scenario:**

→A seeker searches for mentorship groups using the search bar and filters, joins suggested communities, and gains access to shared content and events.

### **Use Case 3: Engage with Active Communities**

**Scenario:**

→A user browses the list of most active communities and joins ongoing conversations to stay connected with like-minded peers, with view and download the resources.

### **Use Case 4: Participate in Discussions**

**Scenario:**

→A seeker joins topic-based discussions, shares questions, and marks topics they’re interested in to receive relevant updates.

### **Use Case 5: Identify Active Contributors**

**Scenario:**

→Users view a leaderboard of active contributors in a community, connect with them, and gain mentorship from highly engaged members.

### **Use Case 6: Check Upcoming Events**

**Scenario:**

→A user reviews event details such as name, date, time, and number of attendees, and registers for events relevant to their mentorship goals.

---

1. Settings

### **Use Case 1: Manage Notification Settings**

**Scenario:**

→A user customizes notifications by enabling or disabling email alerts, calendar reminders with notes, browser push notifications, message alerts, weekly digests, and notifications for anonymous pool responses.

### **Use Case 2: Review Privacy Disclaimer**

**Scenario:**

→A seeker opens the privacy disclaimer to understand how their personal data, activity, and identity are handled by the platform.

### **Use Case 3: Access Platform Policies**

**Scenario:**

→A user reviews subscription, reporting, referral, and payment policies to stay informed about platform rules and procedures.

### **Use Case 4: Enable Multi-Factor Authentication (MFA)**

**Scenario:**

→A user activates MFA, adding an extra verification step (e.g., OTP or authenticator app) when signing in for enhanced account security.

---

1. Support

### **Use Case 1: Contact via Email**

**Scenario:**

→A user encounters a technical issue <NAME_EMAIL>. The support team responds with troubleshooting steps or escalates the issue if needed.

### **Use Case 2: Get Real-Time Help via Chat**

**Scenario:**

→A seeker needs quick assistance with booking and opens the Help Desk Chat, receiving instant support from an agent.

### **Use Case 3: Resolve Payment Queries**

**Scenario:**

→A user has a billing issue and raises a payment-related query through support. The support team checks transaction details and provides resolution.

### **Use Case 4: Browse FAQs**

**Scenario:**

→A new user explores the FAQ section to find quick answers about chat usage, payment processes, refunds, and platform policies.

### **Use Case 5: Manage Support Chats**

**Scenario:**

→A user reviews previous support conversations, clears unnecessary messages, or deletes entire chat histories for privacy.

---

LOGOUT